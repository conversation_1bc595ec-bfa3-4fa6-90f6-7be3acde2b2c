using UnityEngine;
using UnityEngine.UI;

namespace F8Framework.Core
{
    // UGUI建筑物层级设置组件
    public class UIBuildingLayerSetter : MonoBehaviour
    {
        [SerializeField] private bool _alwaysInFront = false;
        [SerializeField] private bool _alwaysInBack = false;
        [SerializeField] private int _orderInLayer = 0;
        [SerializeField] private bool _registerWithSubmarine = true;

        private RectTransform _rectTransform;

        private void Start()
        {
            _rectTransform = GetComponent<RectTransform>();
            if (_rectTransform == null)
            {
                LogF8.LogError("UIBuildingLayerSetter requires a RectTransform component.");
                return;
            }

            UIGameObjectLayer layer;

            if (_alwaysInFront)
            {
                layer = UIGameObjectLayer.BuildingFront;
            }
            else if (_alwaysInBack)
            {
                layer = UIGameObjectLayer.BuildingBack;
            }
            else
            {
                // 默认层级，可以根据其他条件动态调整
                layer = UIGameObjectLayer.Submarine;
            }

            // 🔧 修复：添加实例空值检查
            if (UIRenderOrderManager.Instance != null)
            {
                UIRenderOrderManager.Instance.SetObjectLayer(gameObject, layer, _orderInLayer);
            }

            // 如果需要，将建筑物注册到潜艇控制器
            if (_registerWithSubmarine)
            {
                UISubmarineController submarineController = FindObjectOfType<UISubmarineController>();
                if (submarineController != null)
                {
                    submarineController.AddBuilding(_rectTransform);
                }
            }
        }

        private void OnDestroy()
        {
            // 从潜艇控制器中移除建筑物
            if (_registerWithSubmarine && _rectTransform != null)
            {
                UISubmarineController submarineController = FindObjectOfType<UISubmarineController>();
                if (submarineController != null)
                {
                    submarineController.RemoveBuilding(_rectTransform);
                }
            }

            // 🔧 修复：添加实例空值检查，防止应用程序关闭时的空引用异常
            if (UIRenderOrderManager.Instance != null)
            {
                UIRenderOrderManager.Instance.ClearObjectLayer(gameObject);
            }
        }
    }
}