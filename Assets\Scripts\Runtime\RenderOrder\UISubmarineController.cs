using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace F8Framework.Core
{
    // UI潜艇层级控制组件
    public class UISubmarineController : MonoBehaviour
    {
        [SerializeField] private RectTransform _submarineRect;
        [SerializeField] private List<RectTransform> _buildingRects = new List<RectTransform>();
        [SerializeField] private float _checkInterval = 0.1f; // 检查间隔时间

        private float _lastCheckTime;

        private void Start()
        {
            if (_submarineRect == null)
            {
                _submarineRect = GetComponent<RectTransform>();
            }

            // 🔧 修复：添加实例空值检查
            if (UIRenderOrderManager.Instance != null)
            {
                // 初始设置潜艇层级
                UIRenderOrderManager.Instance.SetObjectLayer(gameObject, UIGameObjectLayer.Submarine);
            }
        }

        private void Update()
        {
            // 定时检查，避免每帧都检查
            if (Time.time - _lastCheckTime < _checkInterval)
                return;

            _lastCheckTime = Time.time;

            // 检测与建筑物的重叠
            List<RectTransform> overlappingBuildings = DetectOverlappingBuildings();

            if (overlappingBuildings.Count > 0)
            {
                // 根据游戏逻辑决定潜艇应该在前面还是后面
                bool shouldBeInFront = ShouldBeInFront(overlappingBuildings);

                // 🔧 修复：添加实例空值检查
                if (UIRenderOrderManager.Instance != null)
                {
                    // 更新潜艇的层级
                    UIGameObjectLayer newLayer = shouldBeInFront ?
                        UIGameObjectLayer.BuildingFront : UIGameObjectLayer.BuildingBack;

                    UIRenderOrderManager.Instance.UpdateObjectLayer(gameObject, newLayer+1);
                }
            }
            else
            {
                // 🔧 修复：添加实例空值检查
                if (UIRenderOrderManager.Instance != null)
                {
                    // 没有重叠的建筑物，恢复默认层级
                    UIRenderOrderManager.Instance.UpdateObjectLayer(gameObject, UIGameObjectLayer.Submarine);
                }
            }
        }

        // 检测与潜艇重叠的建筑物
        private List<RectTransform> DetectOverlappingBuildings()
        {
            List<RectTransform> overlappingBuildings = new List<RectTransform>();

            foreach (var buildingRect in _buildingRects)
            {
                if (buildingRect == null) continue;

                // 检查矩形是否重叠
                if (RectTransformsOverlap(_submarineRect, buildingRect))
                {
                    overlappingBuildings.Add(buildingRect);
                }
            }

            return overlappingBuildings;
        }

        // 检查两个RectTransform是否重叠
        private bool RectTransformsOverlap(RectTransform rectA, RectTransform rectB)
        {
            // 将两个矩形转换到屏幕空间
            Vector3[] cornersA = new Vector3[4];
            Vector3[] cornersB = new Vector3[4];

            rectA.GetWorldCorners(cornersA);
            rectB.GetWorldCorners(cornersB);

            // 检查矩形是否重叠
            // 如果一个矩形的最右边小于另一个的最左边，或者最左边大于另一个的最右边，则不重叠
            // 同理，如果一个矩形的最上边小于另一个的最下边，或者最下边大于另一个的最上边，则不重叠
            float minAX = Mathf.Min(cornersA[0].x, cornersA[1].x, cornersA[2].x, cornersA[3].x);
            float maxAX = Mathf.Max(cornersA[0].x, cornersA[1].x, cornersA[2].x, cornersA[3].x);
            float minAY = Mathf.Min(cornersA[0].y, cornersA[1].y, cornersA[2].y, cornersA[3].y);
            float maxAY = Mathf.Max(cornersA[0].y, cornersA[1].y, cornersA[2].y, cornersA[3].y);

            float minBX = Mathf.Min(cornersB[0].x, cornersB[1].x, cornersB[2].x, cornersB[3].x);
            float maxBX = Mathf.Max(cornersB[0].x, cornersB[1].x, cornersB[2].x, cornersB[3].x);
            float minBY = Mathf.Min(cornersB[0].y, cornersB[1].y, cornersB[2].y, cornersB[3].y);
            float maxBY = Mathf.Max(cornersB[0].y, cornersB[1].y, cornersB[2].y, cornersB[3].y);

            return !(maxAX < minBX || minAX > maxBX || maxAY < minBY || minAY > maxBY);
        }

        // 根据游戏逻辑决定潜艇是否应该在建筑物前面
        private bool ShouldBeInFront(List<RectTransform> overlappingBuildings)
        {
            // 示例逻辑：根据Y坐标比较
            // 如果潜艇的Y坐标大于建筑物的Y坐标，则潜艇在建筑物前面
            float submarineY = _submarineRect.position.y;

            foreach (var buildingRect in overlappingBuildings)
            {
                if (submarineY < buildingRect.position.y)
                {
                    return true;
                }
            }

            return false;
        }

        // 添加建筑物到检测列表
        public void AddBuilding(RectTransform buildingRect)
        {
            if (!_buildingRects.Contains(buildingRect))
            {
                _buildingRects.Add(buildingRect);
            }
        }

        // 从检测列表中移除建筑物
        public void RemoveBuilding(RectTransform buildingRect)
        {
            _buildingRects.Remove(buildingRect);
        }
    }
}