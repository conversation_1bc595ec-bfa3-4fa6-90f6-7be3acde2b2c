using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace F8Framework.Core
{
    // UI游戏对象层级枚举
    public enum UIGameObjectLayer
    {
        Background = 0,       // 背景层
        Ground = 100,         // 地面层
        BuildingBack = 200,   // 建筑物后层（显示在潜艇后面）
        Submarine = 300,      // 潜艇层
        BuildingFront = 400,  // 建筑物前层（显示在潜艇前面）
        Effect = 500,         // 特效层
        UI = 600              // UI层
    }

    // UI渲染顺序管理器
    public class UIRenderOrderManager : ModuleSingletonMono<UIRenderOrderManager>, IModule
    {
        // 存储每个层级内的对象数量，用于计算同层级内的顺序
        private Dictionary<UIGameObjectLayer, int> _layerCounter = new Dictionary<UIGameObjectLayer, int>();

        // 存储对象的层级信息
        private Dictionary<GameObject, UILayerInfo> _objectLayers = new Dictionary<GameObject, UILayerInfo>();

        // 层级信息类
        private class UILayerInfo
        {
            public UIGameObjectLayer Layer;
            public int OrderInLayer;
            public Renderer Renderer;  // 用于SpriteRenderer等
            public Canvas Canvas;      // 用于UGUI元素
            public Graphic Graphic;    // 用于UI图形元素

            public UILayerInfo(UIGameObjectLayer layer, int orderInLayer, Renderer renderer, Canvas canvas, Graphic graphic)
            {
                Layer = layer;
                OrderInLayer = orderInLayer;
                Renderer = renderer;
                Canvas = canvas;
                Graphic = graphic;
            }
        }

        public void OnInit(object createParam)
        {
            // 初始化层级计数器
            foreach (UIGameObjectLayer layer in System.Enum.GetValues(typeof(UIGameObjectLayer)))
            {
                _layerCounter[layer] = 0;
            }
        }

        // 设置对象的层级
        public void SetObjectLayer(GameObject gameObject, UIGameObjectLayer layer, int? orderInLayer = null)
        {
            // 🔧 修复：添加空值检查
            if (gameObject == null)
            {
                LogF8.LogError("UIRenderOrderManager.SetObjectLayer: GameObject is null");
                return;
            }

            Renderer renderer = gameObject.GetComponent<Renderer>();
            Canvas canvas = gameObject.GetComponent<Canvas>();
            Graphic graphic = gameObject.GetComponent<Graphic>();

            if (renderer == null && canvas == null && graphic == null)
            {
                // 尝试在子对象中查找
                renderer = gameObject.GetComponentInChildren<Renderer>();
                canvas = gameObject.GetComponentInChildren<Canvas>();
                graphic = gameObject.GetComponentInChildren<Graphic>();

                if (renderer == null && canvas == null && graphic == null)
                {
                    LogF8.LogError($"GameObject {gameObject.name} does not have a Renderer, Canvas or Graphic component.");
                    return;
                }
            }

            // 如果没有指定orderInLayer，则自动分配
            int order = orderInLayer ?? GetNextOrderInLayer(layer);

            // 存储层级信息
            _objectLayers[gameObject] = new UILayerInfo(layer, order, renderer, canvas, graphic);

            // 设置排序顺序
            int sortingOrder = (int)layer + order;

            if (canvas != null)
            {
                // 对于Canvas元素，设置Canvas的排序顺序
                canvas.overrideSorting = true;
                canvas.sortingOrder = sortingOrder;
            }

            if (renderer != null)
            {
                // 对于普通游戏对象，设置Renderer的排序顺序
                renderer.sortingOrder = sortingOrder;
            }

            if (graphic != null && canvas == null)
            {
                // 对于UI元素但没有Canvas的情况，我们需要添加一个Canvas
                Canvas newCanvas = gameObject.AddComponent<Canvas>();
                newCanvas.overrideSorting = true;
                newCanvas.sortingOrder = sortingOrder;

                // 更新层级信息
                _objectLayers[gameObject] = new UILayerInfo(layer, order, renderer, newCanvas, graphic);
            }
        }

        // 获取层级内的下一个顺序值
        private int GetNextOrderInLayer(UIGameObjectLayer layer)
        {
            int order = _layerCounter[layer];
            _layerCounter[layer]++;
            return order;
        }

        // 动态调整对象的层级
        public void UpdateObjectLayer(GameObject gameObject, UIGameObjectLayer newLayer, int? newOrderInLayer = null)
        {
            // 🔧 修复：添加空值检查
            if (gameObject == null)
            {
                LogF8.LogError("UIRenderOrderManager.UpdateObjectLayer: GameObject is null");
                return;
            }

            if (!_objectLayers.TryGetValue(gameObject, out UILayerInfo info))
            {
                SetObjectLayer(gameObject, newLayer, newOrderInLayer);
                return;
            }

            info.Layer = newLayer;
            if (newOrderInLayer.HasValue)
            {
                info.OrderInLayer = newOrderInLayer.Value;
            }

            // 更新排序顺序
            int sortingOrder = (int)newLayer + info.OrderInLayer;

            if (info.Canvas != null)
            {
                info.Canvas.sortingOrder = sortingOrder;
            }

            if (info.Renderer != null)
            {
                info.Renderer.sortingOrder = sortingOrder;
            }
        }

        // 获取对象当前的层级
        public UIGameObjectLayer? GetObjectLayer(GameObject gameObject)
        {
            // 🔧 修复：添加空值检查
            if (gameObject == null)
            {
                LogF8.LogError("UIRenderOrderManager.GetObjectLayer: GameObject is null");
                return null;
            }

            if (_objectLayers.TryGetValue(gameObject, out UILayerInfo info))
            {
                return info.Layer;
            }
            return null;
        }

        // 清理对象的层级信息（当对象被销毁时调用）
        public void ClearObjectLayer(GameObject gameObject)
        {
            // 🔧 修复：添加空值检查和日志
            if (gameObject == null)
            {
                LogF8.LogWarning("UIRenderOrderManager.ClearObjectLayer: GameObject is null");
                return;
            }

            if (_objectLayers.Remove(gameObject))
            {
                LogF8.Log($"UIRenderOrderManager: Cleared layer info for {gameObject.name}");
            }
        }

        // 实现IModule接口的其他方法
        public void OnUpdate()
        {
            // 🔧 修复：定期清理无效的GameObject引用
            CleanupInvalidReferences();
        }

        public void OnLateUpdate() { }
        public void OnFixedUpdate() { }

        public void OnTermination()
        {
            // 🔧 修复：在模块销毁时清理所有资源
            LogF8.Log("UIRenderOrderManager: OnTermination - 清理所有资源");

            // 清理所有层级信息
            _objectLayers.Clear();
            _layerCounter.Clear();

            LogF8.Log("UIRenderOrderManager: 资源清理完成");
        }

        // 🔧 新增：清理无效的GameObject引用
        private void CleanupInvalidReferences()
        {
            // 每60帧检查一次（约1秒）
            if (Time.frameCount % 60 != 0) return;

            var keysToRemove = new List<GameObject>();

            foreach (var kvp in _objectLayers)
            {
                if (kvp.Key == null)
                {
                    keysToRemove.Add(kvp.Key);
                }
            }

            foreach (var key in keysToRemove)
            {
                _objectLayers.Remove(key);
            }

            if (keysToRemove.Count > 0)
            {
                LogF8.Log($"UIRenderOrderManager: 清理了 {keysToRemove.Count} 个无效的GameObject引用");
            }
        }
    }
}