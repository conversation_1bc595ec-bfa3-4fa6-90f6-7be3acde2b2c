using System.Collections.Generic;
using UnityEngine;
using QFramework;
using QHLC.ScriptableObjects;
using F8Framework.Core;
using QHLC.Events;
using QHLC.Utilities;

namespace QHLC.Models
{
    /// <summary>
    /// 灯光系统数据模型
    /// </summary>
    public class LightModel : AbstractModel
    {
        // 当前选中的灯光索引
        public BindableProperty<int> CurrentLightIndex = new BindableProperty<int>(0);

        // 灯光总数
        public BindableProperty<int> TotalLights = new BindableProperty<int>(3);

        // 所有灯光是否都已激活
        public BindableProperty<bool> AllLightsActivated = new BindableProperty<bool>(false);

        // 🔧 修复：添加标志位防止重复发送AllLightsActivatedEvent事件
        private bool hasAllLightsActivatedEventSent = false;

        // 配置引用
        private LightConfig lightConfig;

        // 灯光设置 - 从配置中获取
        public float FlashInterval => lightConfig != null ? lightConfig.flashInterval : 0.5f;
        public int FlashCount => lightConfig != null ? lightConfig.flashCount : 3;
        public float ActivationChance => lightConfig != null ? lightConfig.activationChance : 0.3f;
        public Color InactiveColor => lightConfig != null ? lightConfig.inactiveColor : Color.gray;
        public Color ActiveColor => lightConfig != null ? lightConfig.activeColor : Color.green;
        public Color FlashColor => lightConfig != null ? lightConfig.flashColor : Color.yellow;

        protected override void OnInit()
        {
            // 加载配置
            ConfigManager configManager = GameObject.FindObjectOfType<ConfigManager>();
            if (configManager != null)
            {
                lightConfig = configManager.GetLightConfig();
                // 从配置中设置灯光总数
                if (lightConfig != null)
                {
                    TotalLights.Value = lightConfig.totalLights;
                    LogF8.Log($"LightModel: 从配置中设置总灯光数为 {TotalLights.Value}");
                }
            }
            else
            {
                LogF8.LogWarning("ConfigManager not found, using default values");
                lightConfig = ScriptableObject.CreateInstance<LightConfig>();
            }

            // 监听CurrentLightIndex变化，检查是否所有灯光都已激活
            CurrentLightIndex.Register(value =>
            {
                // 当当前灯光索引等于灯光总数时，表示所有灯光都已激活
                bool wasActivated = AllLightsActivated.Value;
                AllLightsActivated.Value = (value >= TotalLights.Value);

                // 如果状态发生变化，输出调试信息
                if (AllLightsActivated.Value != wasActivated)
                {
                    ModuleLogManager.LogLight($"LightModel: 灯光激活状态变化 - 当前索引 = {value}, 总灯光数 = {TotalLights.Value}, 所有灯光已激活 = {AllLightsActivated.Value}");

                    // 🔧 修复：当所有灯光激活且事件未发送时，发送AllLightsActivatedEvent事件
                    if (AllLightsActivated.Value && !hasAllLightsActivatedEventSent)
                    {
                        hasAllLightsActivatedEventSent = true;
                        ModuleLogManager.LogLight("LightModel: 发送AllLightsActivatedEvent事件");
                        TypeEventSystem.Global.Send(new AllLightsActivatedEvent());
                    }
                }
            });

            // 监听TotalLights变化，确保AllLightsActivated状态正确
            TotalLights.Register(value =>
            {
                // 当灯光总数变化时，重新检查是否所有灯光都已激活
                bool wasActivated = AllLightsActivated.Value;
                AllLightsActivated.Value = (CurrentLightIndex.Value >= value);
                ModuleLogManager.LogLight($"LightModel: 总灯光数变化 - 当前索引 = {CurrentLightIndex.Value}, 新总灯光数 = {value}, 所有灯光已激活 = {AllLightsActivated.Value}");

                // 🔧 修复：当所有灯光激活且事件未发送时，发送AllLightsActivatedEvent事件
                if (AllLightsActivated.Value && !wasActivated && !hasAllLightsActivatedEventSent)
                {
                    hasAllLightsActivatedEventSent = true;
                    ModuleLogManager.LogLight("LightModel: 发送AllLightsActivatedEvent事件");
                    TypeEventSystem.Global.Send(new AllLightsActivatedEvent());
                }
            });
        }

        /// <summary>
        /// 激活下一个灯光
        /// </summary>
        public void ActivateNextLight()
        {
            if (CurrentLightIndex.Value < TotalLights.Value)
            {
                CurrentLightIndex.Value++;
            }
        }

        /// <summary>
        /// 重置灯光状态
        /// </summary>
        /// <param name="cacheSpinCount">是否缓存转盘次数，当灯光全部点亮时调用</param>
        public void ResetLights(bool cacheSpinCount = false)
        {
            // 如果需要缓存转盘次数，并且所有灯光都已经激活
            if (cacheSpinCount && AllLightsActivated.Value)
            {
                // 获取WheelBufferSystem并增加缓存的转盘次数
                var wheelBufferSystem = QHLCArchitecture.Interface.GetSystem<WheelBufferSystem>();
                if (wheelBufferSystem != null)
                {
                    wheelBufferSystem.AddBufferedSpinCount(1);
                }
                else
                {
                    ModuleLogManager.LogLightError("LightModel: 无法获取WheelBufferSystem");
                }
            }

            // 🔧 修复：重置灯光状态时，同时重置事件发送标志位
            CurrentLightIndex.Value = 0;
            AllLightsActivated.Value = false;
            hasAllLightsActivatedEventSent = false;
            ModuleLogManager.LogLight("LightModel: 灯光状态已重置，事件发送标志位已重置");
        }
    }
}
