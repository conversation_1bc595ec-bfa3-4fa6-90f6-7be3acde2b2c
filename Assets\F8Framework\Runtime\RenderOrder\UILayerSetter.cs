using UnityEngine;
using UnityEngine.UI;

namespace F8Framework.Core
{
    // 自动设置UI游戏对象层级的组件
    public class UILayerSetter : MonoBehaviour
    {
        [SerializeField] private UIGameObjectLayer _layer;
        [SerializeField] private int _orderInLayer = 0;

        private void Start()
        {
            // 🔧 修复：添加实例空值检查
            if (UIRenderOrderManager.Instance != null)
            {
                // 设置对象的层级
                UIRenderOrderManager.Instance.SetObjectLayer(gameObject, _layer, _orderInLayer);
            }
        }

        private void OnDestroy()
        {
            // 🔧 修复：添加实例空值检查，防止应用程序关闭时的空引用异常
            if (UIRenderOrderManager.Instance != null)
            {
                // 清理层级信息
                UIRenderOrderManager.Instance.ClearObjectLayer(gameObject);
            }
        }
    }
}