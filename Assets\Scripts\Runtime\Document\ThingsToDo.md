# QHLC项目待办事项清单

## PathKit优化任务

### P0级任务（紧急且重要）
- [x] 实现路径点对象池
  - [x] 设计对象池接口
  - [x] 实现路径点预制体
  - [x] 添加对象池管理器
  - [x] 集成到PathKit系统

- [x] 完善错误处理机制
  - [x] 添加异常类型定义
  - [x] 实现错误日志记录
  - [x] 添加错误恢复机制
  - [x] 完善错误提示信息

### P1级任务（重要）
- [x] 实现路径平滑处理
  - [x] 添加路径插值算法
  - [x] 实现速度渐变
  - [x] 优化转向逻辑
  - [x] 添加平滑参数配置

- [x] 添加可视化调试工具
  - [x] 实现路径可视化
  - [x] 添加调试面板
  - [x] 支持运行时路径编辑
  - [x] 添加性能监控

### P2级任务（重要不紧急）
- [x] 实现四叉树优化
  - [x] 设计四叉树数据结构
  - [x] 实现空间划分
  - [x] 优化路径点查找
  - [x] 添加可视化调试

- [x] 添加预设路径功能
  - [x] 实现矩形路径生成
  - [x] 实现圆形路径生成
  - [x] 实现正弦波路径生成
  - [x] 实现之字形路径生成
  - [x] 实现螺旋形路径生成
  - [x] 添加路径配置接口

- [ ] 添加路径编辑器工具
  - [ ] 设计编辑器界面
  - [x] 实现路径预览
  - [ ] 支持路径导入导出
  - [ ] 添加快捷编辑功能

### P3级任务（新增优化任务）
- [x] 接口兼容性优化
  - [x] 统一命名空间
  - [x] 添加兼容性接口
  - [x] 编写迁移指南
  - [x] 添加废弃警告

- [x] 性能优化
  - [x] 实现路径点缓存
  - [x] 优化内存占用
  - [x] 添加性能分析工具
  - [x] 实现动态LOD

- [x] 代码架构重构
  - [x] 将PathManager重构为模块化设计
  - [x] 创建专门的控制器类
  - [x] 添加验证器和日志记录器
  - [x] 实现配置管理器
  - [x] 提高代码可维护性和可测试性

## 任务完成标准
1. 所有功能必须有单元测试覆盖
2. 必须编写完整的文档说明
3. 必须通过代码审查
4. 性能指标必须达到要求：
   - 对象池：内存增长<1MB
   - 路径平滑：帧率保持60FPS
   - 四叉树：查找时间<1ms
   - 内存占用：峰值<10MB

## 进度跟踪
- 开始日期：2024-03-21
- 预计完成：2024-04-21
- 当前进度：100%
- 负责人：PathKit开发组

## 注意事项
1. 优先完成P0级任务
2. 每完成一个任务需要更新文档
3. 遵循QHLC项目规范
4. 保持向后兼容性
5. 定期同步进度
6. 重点关注性能优化
7. 保证代码质量

## 近期计划
1. ~~完成调试面板开发~~
2. ~~实现性能监控功能~~
3. ~~完成四叉树优化~~
4. ~~完成迁移指南编写~~
5. ~~添加废弃警告~~
6. ~~实现路径点缓存~~
7. ~~添加性能分析工具~~
8. ~~实现动态LOD~~
9. ~~添加预设路径功能~~
10. ~~完成PathManager类重构~~
11. 开始路径编辑器工具开发

- [x] 使用PathKit实现Scripts目录下潜艇的移动逻辑（已做好备份，无用的脚本可以直接删除）

- [x] 移除Submarine模块中的旧移动逻辑，完全迁移到PathKit
  - [x] 删除SubmarineMovement.cs
  - [x] 修改SubmarineView.cs，移除对SubmarineMovement的引用
  - [x] 修改SubmarineController.cs，移除旧的移动协程
  - [x] 确保所有移动逻辑只使用PathKit

- [x] 实现转盘闲置状态下的低速旋转功能
  - [x] 在WheelIdleState中添加低速旋转动画
  - [x] 确保旋转动画是无限循环的
  - [x] 确保转盘在停下来的位置符合需要出奖的结果

- [x] 优化转盘旋转动画，确保从当前角度开始旋转
  - [x] 修改WheelUtility.CreateWheelRotationAnimation方法
  - [x] 确保旋转动画总是从当前角度开始
  - [x] 添加详细的日志输出，跟踪旋转角度的计算过程

- [x] 优化转盘旋转动画，让转盘先转到0度位置再进入减速阶段
  - [x] 修改WheelUtility.CreateWheelRotationAnimation方法的匀速旋转阶段
  - [x] 确保匀速旋转阶段结束时转盘角度为0度
  - [x] 确保减速阶段从0度开始旋转到目标角度
  - [x] 确保旋转至少完成指定的额外旋转圈数

- [x] 完善ModuleLogManager功能
  - [x] 添加通用日志方法（Log、LogWarning、LogError）
  - [x] 添加通用日志开关控制
  - [x] 更新ModuleLogController支持通用日志
  - [x] 更新ProgrammingGuidelines.md文档
  - [ ] 考虑添加更多模块的日志支持（如建筑、奖励等）
  - [ ] 添加日志级别控制（如DEBUG、INFO、WARNING、ERROR）
  - [ ] 添加日志输出到文件的功能

- [x] 更新建筑系统文档
  - [x] 在GameDesignDocument.md中添加详细的建筑系统设计文档
  - [x] 更新Changelog.md记录文档更新
  - [x] 创建建筑系统UI设计文档
  - [ ] 添加建筑系统的API文档
  - [ ] 创建建筑系统的使用教程

- [x] 优化建筑系统UI
  - [x] 简化BuildingView组件，只保留必要的UI元素
  - [x] 更新BuildingController，适应简化后的BuildingView
  - [x] 更新BuildingSystemUIDesign.md文档
  - [ ] 优化BuildingView的性能
  - [ ] 实现BuildingView的对象池

- [ ] 增强建筑系统功能
  - [ ] 添加建筑等级系统
    - [ ] 实现建筑升级机制
    - [ ] 添加升级效果和视觉反馈
    - [ ] 实现升级所需资源计算
  - [ ] 添加建筑解锁系统
    - [ ] 实现建筑解锁条件
    - [ ] 添加解锁动画和提示
    - [ ] 实现解锁进度保存
  - [ ] 优化奖励收集体验
    - [ ] 改进奖励飞向UI的动画效果
    - [ ] 添加奖励收集音效
    - [ ] 实现奖励收集特效
  - [ ] 添加建筑交互多样性
    - [ ] 实现不同类型建筑的特殊交互
    - [ ] 添加建筑状态变化效果
    - [ ] 实现建筑维护机制

- [x] 修复AfterEffectsToUnity导出脚本的兼容性问题
  - [x] 修改AfterEffectsToUnityCodeConverter.js文件
    - [x] 添加对Unity 2019+的支持
    - [x] 添加对TextMeshPro的支持
    - [x] 修复父子关系设置错误的问题
    - [x] 确保导出的代码包含正确的命名空间引用
  - [x] 修改AfterEffectsToUnityCodeConverter.jsx文件
    - [x] 优化UI界面，使其更加友好
    - [x] 添加更详细的说明和提示
    - [x] 改进文件保存流程
  - [x] 创建AfterEffectsToUnityImplementation.cs文件
    - [x] 提供基本的实现，使导出的CS文件能够正确编译
    - [x] 实现AfterEffectsAnimation、AfterEffectsInstance等类
    - [x] 实现AfterEffectsUtil工具类

- [ ] 实现数值策划对接系统
  - [ ] 阶段一：设计数据结构和配置文件格式
    - [ ] 创建GameValueConfig ScriptableObject
    - [ ] 设计模块化的配置文件结构
    - [ ] 为每个游戏系统创建独立的配置文件
  - [ ] 阶段二：开发配置文件编辑工具
    - [ ] 创建自定义编辑器
    - [ ] 开发配置文件管理工具
    - [ ] 实现数据导入/导出功能
  - [ ] 阶段三：重构现有系统
    - [ ] 修改GameValueSystem
    - [ ] 修改BuildingValueSystem
    - [ ] 更新其他相关系统
  - [ ] 阶段四：开发数值调试和分析工具
    - [ ] 创建运行时数值查看器
    - [ ] 实现数值分析工具
    - [ ] 测试和优化

- [ ] 增强GameDataModule，添加数值控制和统计功能
  - [ ] 添加游戏会话统计数据
    - [ ] 游戏会话时长统计
    - [ ] 投币、转盘、移动步数统计
    - [ ] 各类游戏事件计数
  - [ ] 添加历史累计数据
    - [ ] 历史总投币、转盘、移动步数
    - [ ] 历史游戏时长
    - [ ] 历史最高记录
  - [ ] 添加游戏平衡参数
    - [ ] 转盘结果概率调整
    - [ ] 灯光激活概率调整
    - [ ] 动态难度调整系统
  - [ ] 实现数据持久化
    - [ ] 使用StorageManager保存数据
    - [ ] 实现数据加载和初始化
    - [ ] 添加数据重置功能
  - [ ] 添加性能监控
    - [ ] 帧率统计
    - [ ] 内存使用统计
    - [ ] 关键操作耗时统计

- [x] 清理MainUIView中的冗余逻辑
  - [x] 删除与GameController重复的事件处理逻辑
  - [x] 移除不再使用的方法和注释
  - [x] 简化事件监听，只保留UI相关的事件
  - [x] 将游戏启动逻辑委托给GameController处理
  - [x] 清理不必要的using语句
  - [x] 删除未使用的ShowUI方法

- [x] 修复Building系统
  - [x] 恢复BuildingController中被注释掉的位置设置代码
  - [x] 优化建筑位置设置，使用PathItem.Position属性
  - [x] 改进BuildingController的错误处理和日志记录
  - [x] 增强建筑交互事件的健壮性
  - [x] 优化奖励收集事件的处理逻辑
  - [x] 添加空值检查和异常处理
  - [x] 改进日志输出，提供更详细的调试信息

- [x] 修复重复添加转盘次数的bug
  - [x] 分析AllLightsActivatedEvent事件重复发送的问题
  - [x] 在LightModel中添加标志位防止重复发送事件
  - [x] 移除LightView、LightHFSM、LightStates中的重复事件发送代码
  - [x] 统一事件发送逻辑，确保AllLightsActivatedEvent只发送一次
  - [x] 在重置灯光时重置事件发送标志位
  - [x] 统一AddBufferedSpinCount调用位置，移除重复调用
  - [x] 移除LightModel.ResetLights方法中的cacheSpinCount参数和相关逻辑
  - [x] 修改LightHFSM和LightStates中的ResetLights(true)调用为ResetLights()
  - [x] 确保转盘缓存只在两个地方增加：正常游戏流程(LightSystem)和奖励收集(RewardSystem)
