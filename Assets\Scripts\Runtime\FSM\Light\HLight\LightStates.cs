using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using QFramework;
using QHLC.Controllers;
using QHLC.Models;
using QHLC.Views;
using QHLC.Events;
using QHLC.Utilities;
using QHLC.FSM;

namespace QHLC.FSM.HLight
{
    /// <summary>
    /// 灯光闲置状态实现
    /// </summary>
    public class LightIdleHState : AbstractHState<LightStateType, LightView>
    {
        private InputBufferSystem inputBufferSystem;

        public LightIdleHState(HFSM<LightStateType> fsm, LightView target) : base(fsm, target)
        {
            inputBufferSystem = target.GetArchitecture().GetSystem<InputBufferSystem>();
        }

        protected override void OnEnter()
        {
            ModuleLogManager.LogLight("LightIdleHState: 进入灯光闲置状态");
        }

        protected override void OnUpdate()
        {
            // 在灯光闲置状态下，处理缓存的输入事件
            if (inputBufferSystem != null && inputBufferSystem.HasBufferedInputs())
            {
                ModuleLogManager.LogLight("LightIdleHState: 检测到输入缓存，开始处理并通知GameFSMCoordinator开始游戏流程");

                // 处理下一个输入事件
                inputBufferSystem.ProcessNextInput();

                // 切换到激活中状态
                mFSM.ChangeState(LightStateType.Activating);

                // 通过GameFSMCoordinator单例请求游戏状态切换
                if (GameFSMCoordinator.Instance != null)
                {
                    GameFSMCoordinator.Instance.RequestStateChange(GameStateType.FlashingLights);
                }
                else
                {
                    ModuleLogManager.LogLight("LightIdleHState: GameFSMCoordinator单例未找到，无法请求状态切换");
                }
            }
        }

        protected override void OnExit()
        {
            ModuleLogManager.LogLight("LightIdleHState: 退出灯光闲置状态");
        }
    }

    /// <summary>
    /// 灯光激活中状态实现
    /// </summary>
    public class LightActivatingHState : AbstractHState<LightStateType, LightView>
    {
        private InputBufferSystem inputBufferSystem;
        private LightModel lightModel;

        public LightActivatingHState(HFSM<LightStateType> fsm, LightView target) : base(fsm, target)
        {
            inputBufferSystem = target.GetArchitecture().GetSystem<InputBufferSystem>();
            lightModel = target.GetModel<LightModel>();
        }

        protected override void OnEnter()
        {
            ModuleLogManager.LogLight("LightActivatingHState: 进入灯光激活中状态");
            // 注册灯光激活事件
            TypeEventSystem.Global.Register<LightActivatedEvent>(OnLightActivated);
        }

        protected override void OnUpdate()
        {
            // 检查是否所有灯光都已激活
            if (lightModel != null && lightModel.AllLightsActivated.Value)
            {
                // 🔧 修复：移除重复的事件发送，事件现在由LightModel统一发送
                ModuleLogManager.LogLight("LightActivatingHState: 所有灯光已激活，切换到已激活状态");
                // 切换到已激活状态
                mFSM.ChangeState(LightStateType.Activated);
            }
            else
            {
                // 在灯光激活中状态下，也处理缓存的输入事件
                if (inputBufferSystem != null && inputBufferSystem.HasBufferedInputs() && !lightModel.AllLightsActivated.Value)
                {
                    // 在激活中状态下，如果还有灯光未激活，则处理输入事件
                    ModuleLogManager.LogLight("LightActivatingHState: 处理缓存的输入事件，点亮下一盆灯光");
                    inputBufferSystem.ProcessNextInput();
                }
            }
        }

        protected override void OnExit()
        {
            ModuleLogManager.LogLight("LightActivatingHState: 退出灯光激活中状态");
            // 注销灯光激活事件
            TypeEventSystem.Global.UnRegister<LightActivatedEvent>(OnLightActivated);
        }

        // 处理灯光激活事件
        private void OnLightActivated(LightActivatedEvent evt)
        {
            ModuleLogManager.LogLight($"LightActivatingHState: 收到灯光激活事件，索引: {evt.LightIndex}");
        }
    }

    /// <summary>
    /// 灯光已激活状态实现
    /// </summary>
    public class LightActivatedHState : AbstractHState<LightStateType, LightView>
    {
        private InputBufferSystem inputBufferSystem;
        private LightModel lightModel;
        private bool animationStarted = false;

        public LightActivatedHState(HFSM<LightStateType> fsm, LightView target) : base(fsm, target)
        {
            inputBufferSystem = target.GetArchitecture().GetSystem<InputBufferSystem>();
            lightModel = target.GetModel<LightModel>();
        }

        protected override void OnEnter()
        {
            ModuleLogManager.LogLight("LightActivatedHState: 进入灯光已激活状态");
            animationStarted = false;
            // 注册灯光动画完成事件
            TypeEventSystem.Global.Register<LightAnimationCompletedEvent>(OnLightAnimationCompleted);
        }

        protected override void OnUpdate()
        {
            // 当所有灯光都被激活时，播放灯光全亮动画
            if (lightModel != null && lightModel.AllLightsActivated.Value && !animationStarted)
            {
                // 播放灯光全亮动画
                mTarget.PlayAllLightsActivatedAnimation();
                animationStarted = true;
            }

            // 在灯光已激活状态下，只缓存输入，不处理
            if (inputBufferSystem != null && inputBufferSystem.HasBufferedInputs())
            {
                ModuleLogManager.LogLight("LightActivatedHState: 检测到缓存的输入事件，但在已激活状态下不处理");
            }
        }

        protected override void OnExit()
        {
            ModuleLogManager.LogLight("LightActivatedHState: 退出灯光已激活状态");
            // 注销灯光动画完成事件
            TypeEventSystem.Global.UnRegister<LightAnimationCompletedEvent>(OnLightAnimationCompleted);
        }

        // 处理灯光动画完成事件
        private void OnLightAnimationCompleted(LightAnimationCompletedEvent evt)
        {
            ModuleLogManager.LogLight("LightActivatedHState: 收到灯光动画完成事件");

            // 动画完成后，重置灯光状态
            if (lightModel != null && lightModel.AllLightsActivated.Value)
            {
                // 重置灯光状态
                lightModel.ResetLights(true);
                // 切换回闲置状态
                mFSM.ChangeState(LightStateType.Idle);
            }
        }
    }
}