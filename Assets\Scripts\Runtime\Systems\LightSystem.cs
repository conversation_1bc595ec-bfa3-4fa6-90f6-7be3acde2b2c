using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using QFramework;
using QHLC.Models;
using QHLC.Events;
using F8Framework.Core;

namespace QHLC
{
    /// <summary>
    /// 灯光系统，负责处理灯光相关的业务逻辑
    /// </summary>
    public class LightSystem : AbstractSystem
    {
        protected override void OnInit()
        {
            // 🔧 修复：使用Global事件系统注册AllLightsActivatedEvent事件监听
            TypeEventSystem.Global.Register<AllLightsActivatedEvent>(OnAllLightsActivated);
            ModuleLogManager.LogLight("LightSystem: 已注册AllLightsActivatedEvent事件监听");
        }

        protected override void OnDeinit()
        {
            // 🔧 注销事件监听
            TypeEventSystem.Global.UnRegister<AllLightsActivatedEvent>(OnAllLightsActivated);
            ModuleLogManager.LogLight("LightSystem: 已注销AllLightsActivatedEvent事件监听");
        }

        /// <summary>
        /// 处理所有灯光激活事件
        /// </summary>
        /// <param name="e">事件数据</param>
        private void OnAllLightsActivated(AllLightsActivatedEvent e)
        {
            ModuleLogManager.LogLight("LightSystem: 收到所有灯光激活事件，准备增加转盘缓存");

            // 获取WheelBufferSystem并增加转盘缓存
            var wheelBufferSystem = this.GetSystem<WheelBufferSystem>();
            if (wheelBufferSystem != null)
            {
                wheelBufferSystem.AddBufferedSpinCount(1);
                ModuleLogManager.LogLight("LightSystem: 成功增加转盘缓存次数");
            }
            else
            {
                ModuleLogManager.LogLightError("LightSystem: 无法获取WheelBufferSystem");
            }
        }

        /// <summary>
        /// 重置灯光状态
        /// </summary>
        public void ResetLights()
        {
            var lightModel = this.GetModel<LightModel>();
            lightModel.ResetLights();
            // 灯光已重置
        }
    }
}
