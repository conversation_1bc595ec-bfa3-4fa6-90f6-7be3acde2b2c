using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using QFramework;
using QHLC.Controllers;
using QHLC.Models;
using QHLC.Views;
using QHLC.Events;
using QHLC.Utilities;
using Sirenix.OdinInspector;

namespace QHLC.FSM.HLight
{
    /// <summary>
    /// 灯光状态枚举
    /// </summary>
    public enum LightStateType
    {
        Idle,       // 闲置状态
        Activating, // 激活中状态
        Activated   // 已激活状态
    }

    /// <summary>
    /// 使用HFSMKit实现的灯光分层状态机
    /// </summary>
    public class LightHFSM : MonoBehaviour
    {
        [SerializeField] private LightView lightView;

        private HFSM<LightStateType> lightFSM;
        private LightStateType previousStateId;
        private bool isInitialized = false;

        [ShowInInspector, ReadOnly, PropertyOrder(-10)]
        [BoxGroup("FSM 状态")]
        [LabelText("当前状态")]
        private string CurrentStateName => lightFSM?.CurrentStateId.ToString() ?? "未初始化";

        [ShowInInspector, ReadOnly, PropertyOrder(-9)]
        [BoxGroup("FSM 状态")]
        [LabelText("上一个状态")]
        private string PreviousStateName => previousStateId.ToString();

        [ShowInInspector, ReadOnly, PropertyOrder(-8)]
        [BoxGroup("FSM 状态")]
        [LabelText("状态机名称")]
        private string FSMName => "LightHFSM";

        [ShowInInspector, ReadOnly, PropertyOrder(-7)]
        [BoxGroup("FSM 状态")]
        [LabelText("是否已初始化")]
        private bool IsInitialized => isInitialized;

        /// <summary>
        /// 获取当前状态ID，供外部访问
        /// </summary>
        public LightStateType? CurrentStateId => lightFSM?.CurrentStateId;

        private void Awake()
        {
            if (lightView == null)
            {
                lightView = GetComponent<LightView>();
                if (lightView == null)
                {
                    ModuleLogManager.LogError("LightHFSM: LightView not found!");
                    return;
                }
            }

            // 注册FSM状态变化事件监听
            TypeEventSystem.Global.Register<FSMStateChangedEvent>(OnFSMStateChanged);
        }

        /// <summary>
        /// 处理FSM状态变化事件
        /// </summary>
        private void OnFSMStateChanged(FSMStateChangedEvent evt)
        {
            // 只处理来自主游戏FSM的事件
            if (evt.FSMType != FSMType.Game) return;

            ModuleLogManager.LogLight($"LightHFSM: 收到GameFSM状态变化事件 - 从 {evt.PreviousState} 到 {evt.CurrentState}");
        }

        /// <summary>
        /// 初始化状态机
        /// </summary>
        public void Initialize()
        {
            if (isInitialized) return;

            // 创建状态机
            lightFSM = new HFSM<LightStateType>();

            // 配置闲置状态
            lightFSM.State(LightStateType.Idle)
                .OnEnter(() => {
                    ModuleLogManager.LogLight("LightHFSM: 进入灯光闲置状态");
                })
                .OnUpdate(() => {
                    // 在灯光闲置状态下，处理缓存的输入事件
                    var inputBufferSystem = lightView.GetArchitecture().GetSystem<InputBufferSystem>();
                    if (inputBufferSystem != null && inputBufferSystem.HasBufferedInputs())
                    {
                        // 处理下一个输入事件
                        inputBufferSystem.ProcessNextInput();
                        // 切换到激活中状态
                        lightFSM.ChangeState(LightStateType.Activating);
                    }
                })
                .OnExit(() => {
                    ModuleLogManager.LogLight("LightHFSM: 退出灯光闲置状态");
                });

            // 配置激活中状态
            lightFSM.State(LightStateType.Activating)
                .OnEnter(() => {
                    ModuleLogManager.LogLight("LightHFSM: 进入灯光激活中状态");
                    // 注册灯光激活事件
                    TypeEventSystem.Global.Register<LightActivatedEvent>(OnLightActivated);
                })
                .OnUpdate(() => {
                    // 检查是否所有灯光都已激活
                    var lightModel = lightView.GetModel<LightModel>();
                    if (lightModel != null && lightModel.AllLightsActivated.Value)
                    {
                        // 🔧 修复：移除重复的事件发送，事件现在由LightModel统一发送
                        ModuleLogManager.LogLight("LightHFSM: 所有灯光已激活，切换到已激活状态");
                        // 切换到已激活状态
                        lightFSM.ChangeState(LightStateType.Activated);
                    }
                    else
                    {
                        // 在灯光激活中状态下，也处理缓存的输入事件
                        var inputBufferSystem = lightView.GetArchitecture().GetSystem<InputBufferSystem>();
                        if (inputBufferSystem != null && inputBufferSystem.HasBufferedInputs() && !lightModel.AllLightsActivated.Value)
                        {
                            // 在激活中状态下，如果还有灯光未激活，则处理输入事件
                            ModuleLogManager.LogLight("LightHFSM: 处理缓存的输入事件，点亮下一盆灯光");
                            inputBufferSystem.ProcessNextInput();
                        }
                    }
                })
                .OnExit(() => {
                    ModuleLogManager.LogLight("LightHFSM: 退出灯光激活中状态");
                    // 注销灯光激活事件
                    TypeEventSystem.Global.UnRegister<LightActivatedEvent>(OnLightActivated);
                });

            // 配置已激活状态
            lightFSM.State(LightStateType.Activated)
                .OnEnter(() => {
                    ModuleLogManager.LogLight("LightHFSM: 进入灯光已激活状态");
                    // 注册灯光动画完成事件
                    TypeEventSystem.Global.Register<LightAnimationCompletedEvent>(OnLightAnimationCompleted);
                    // 当所有灯光都被激活时，播放灯光全亮动画
                    var lightModel = lightView.GetModel<LightModel>();
                    if (lightModel != null && lightModel.AllLightsActivated.Value)
                    {
                        // 播放灯光全亮动画
                        lightView.PlayAllLightsActivatedAnimation();
                    }
                })
                .OnUpdate(() => {
                    // 在灯光已激活状态下，只缓存输入，不处理
                    var inputBufferSystem = lightView.GetArchitecture().GetSystem<InputBufferSystem>();
                    if (inputBufferSystem != null && inputBufferSystem.HasBufferedInputs())
                    {
                        ModuleLogManager.LogLight("LightHFSM: 检测到缓存的输入事件，但在已激活状态下不处理");
                    }
                })
                .OnExit(() => {
                    ModuleLogManager.LogLight("LightHFSM: 退出灯光已激活状态");
                    // 注销灯光动画完成事件
                    TypeEventSystem.Global.UnRegister<LightAnimationCompletedEvent>(OnLightAnimationCompleted);
                });

            // 注册状态变化事件
            lightFSM.OnStateChanged((prevState, nextState) => {
                previousStateId = prevState;
                ModuleLogManager.LogLight($"LightHFSM: 状态从 {prevState} 变为 {nextState}");

                // 发送FSM状态变化事件，通知其他FSM
                TypeEventSystem.Global.Send(new FSMStateChangedEvent
                {
                    FSMType = FSMType.Light,
                    PreviousState = prevState.ToString(),
                    CurrentState = nextState.ToString()
                });
            });

            isInitialized = true;
        }

        /// <summary>
        /// 启动状态机
        /// </summary>
        public void StartFSM()
        {
            if (!isInitialized)
            {
                Initialize();
            }
            lightFSM.StartState(LightStateType.Idle);
        }

        /// <summary>
        /// 获取HFSM实例，用于嵌套状态机
        /// </summary>
        public HFSM<LightStateType> GetFSM()
        {
            if (!isInitialized)
            {
                Initialize();
            }
            return lightFSM;
        }

        /// <summary>
        /// 切换状态
        /// </summary>
        public void ChangeState(LightStateType state)
        {
            if (lightFSM != null)
            {
                lightFSM.ChangeState(state);
            }
        }

        /// <summary>
        /// 处理灯光激活事件
        /// </summary>
        private void OnLightActivated(LightActivatedEvent evt)
        {
            ModuleLogManager.LogLight($"LightHFSM: 收到灯光激活事件，索引: {evt.LightIndex}");
        }

        /// <summary>
        /// 处理灯光动画完成事件
        /// </summary>
        private void OnLightAnimationCompleted(LightAnimationCompletedEvent evt)
        {
            ModuleLogManager.LogLight("LightHFSM: 收到灯光动画完成事件");

            // 动画完成后，重置灯光状态
            var lightModel = lightView.GetModel<LightModel>();
            if (lightModel != null && lightModel.AllLightsActivated.Value)
            {
                // 🔧 修复：移除重复的转盘缓存添加逻辑，转盘缓存已经在AllLightsActivatedEvent中统一处理
                lightModel.ResetLights();
                ModuleLogManager.LogLight("LightHFSM: 灯光动画完成，已重置灯光状态");

                // 切换回闲置状态
                lightFSM.ChangeState(LightStateType.Idle);
            }
        }

        private void Update()
        {
            if (lightFSM != null && isInitialized)
            {
                lightFSM.Update();
            }
        }

        private void OnDestroy()
        {
            if (lightFSM != null)
            {
                lightFSM.Clear();
                lightFSM = null;
            }

            // 注销事件监听
            TypeEventSystem.Global.UnRegister<FSMStateChangedEvent>(OnFSMStateChanged);
        }
    }
}